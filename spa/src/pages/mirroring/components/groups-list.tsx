import { WhatsAppGroup } from '@/types';
import { useWhatsAppInstances } from '@/hooks/useWhatsAppInstances';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Skeleton } from '@/components/ui/skeleton';
import { Avatar, AvatarFallback } from '@/components/ui/avatar';
import { 
  Users, 
  Copy, 
  Calendar, 
  Crown, 
  User,
  MessageSquare
} from 'lucide-react';
import { cn } from '@/lib/utils';

interface GroupsListProps {
  groups: WhatsAppGroup[];
  instanceId: string;
  isLoading: boolean;
}

export function GroupsList({ groups, instanceId, isLoading }: GroupsListProps) {
  const { mirrorGroup, isMirroring } = useWhatsAppInstances();

  const handleMirrorGroup = (groupId: string) => {
    mirrorGroup(instanceId, groupId);
  };

  if (isLoading) {
    return (
      <div className="space-y-4">
        {[...Array(3)].map((_, i) => (
          <Card key={i}>
            <CardContent className="p-4">
              <div className="space-y-3">
                <div className="flex items-center justify-between">
                  <Skeleton className="h-5 w-3/4" />
                  <Skeleton className="h-8 w-20" />
                </div>
                <Skeleton className="h-4 w-1/2" />
                <div className="flex gap-2">
                  <Skeleton className="h-6 w-6 rounded-full" />
                  <Skeleton className="h-6 w-6 rounded-full" />
                  <Skeleton className="h-6 w-6 rounded-full" />
                </div>
              </div>
            </CardContent>
          </Card>
        ))}
      </div>
    );
  }

  if (groups.length === 0) {
    return (
      <div className="text-center py-8">
        <Users className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
        <h3 className="text-lg font-medium">Nenhum grupo encontrado</h3>
        <p className="text-muted-foreground">
          Esta instância não possui grupos WhatsApp ou eles ainda não foram carregados
        </p>
      </div>
    );
  }

  return (
    <div className="space-y-4">
      {groups.map((group) => (
        <Card key={group.id} className="hover:shadow-md transition-shadow">
          <CardContent className="p-4">
            <div className="space-y-4">
              {/* Group Header */}
              <div className="flex items-center justify-between">
                <div className="flex items-center gap-3">
                  <div className="flex items-center justify-center w-10 h-10 bg-primary/10 rounded-full">
                    <Users className="h-5 w-5 text-primary" />
                  </div>
                  <div>
                    <h3 className="font-medium">{group.name}</h3>
                    <p className="text-sm text-muted-foreground">
                      {group.participants.length} participante{group.participants.length !== 1 ? 's' : ''}
                    </p>
                  </div>
                </div>
                
                <Button
                  onClick={() => handleMirrorGroup(group.id)}
                  disabled={isMirroring}
                  size="sm"
                  className="flex items-center gap-2"
                >
                  <Copy className="h-4 w-4" />
                  {isMirroring ? 'Espelhando...' : 'Espelhar Grupo'}
                </Button>
              </div>

              {/* Group Info */}
              <div className="flex items-center gap-4 text-sm text-muted-foreground">
                <div className="flex items-center gap-1">
                  <MessageSquare className="h-3 w-3" />
                  <span>ID: {group.id}</span>
                </div>
                {group.createdAt && (
                  <div className="flex items-center gap-1">
                    <Calendar className="h-3 w-3" />
                    <span>Criado: {new Date(group.createdAt).toLocaleDateString('pt-BR')}</span>
                  </div>
                )}
              </div>

              {/* Participants */}
              <div>
                <h4 className="text-sm font-medium mb-2">Participantes:</h4>
                <div className="space-y-2">
                  {group.participants.slice(0, 5).map((participant) => (
                    <div key={participant.id} className="flex items-center gap-2">
                      <Avatar className="h-6 w-6">
                        <AvatarFallback className="text-xs">
                          {participant.name.charAt(0).toUpperCase()}
                        </AvatarFallback>
                      </Avatar>
                      <span className="text-sm">{participant.name}</span>
                      {participant.isAdmin && (
                        <Badge variant="secondary" className="text-xs">
                          <Crown className="h-3 w-3 mr-1" />
                          Admin
                        </Badge>
                      )}
                    </div>
                  ))}
                  
                  {group.participants.length > 5 && (
                    <div className="text-sm text-muted-foreground">
                      +{group.participants.length - 5} participantes...
                    </div>
                  )}
                </div>
              </div>

              {/* Admins Count */}
              <div className="flex items-center gap-4 text-xs text-muted-foreground pt-2 border-t">
                <div className="flex items-center gap-1">
                  <Crown className="h-3 w-3" />
                  <span>
                    {group.participants.filter(p => p.isAdmin).length} admin{group.participants.filter(p => p.isAdmin).length !== 1 ? 's' : ''}
                  </span>
                </div>
                <div className="flex items-center gap-1">
                  <User className="h-3 w-3" />
                  <span>
                    {group.participants.filter(p => !p.isAdmin).length} membro{group.participants.filter(p => !p.isAdmin).length !== 1 ? 's' : ''}
                  </span>
                </div>
              </div>
            </div>
          </CardContent>
        </Card>
      ))}
    </div>
  );
}
