import { WhatsAppInstance } from '@/types';
import { useWhatsAppInstances } from '@/hooks/useWhatsAppInstances';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Separator } from '@/components/ui/separator';
import { 
  Play, 
  Square, 
  RotateCcw, 
  Phone, 
  User, 
  Calendar, 
  Settings,
  MessageSquare,
  Users,
  Clock,
  Shield,
  Eye,
  EyeOff,
  PhoneCall
} from 'lucide-react';
import { cn } from '@/lib/utils';

interface InstanceDetailsProps {
  instance: WhatsAppInstance;
}

export function InstanceDetails({ instance }: InstanceDetailsProps) {
  const {
    connectInstance,
    disconnectInstance,
    restartInstance,
    isConnecting,
    isDisconnecting,
    isRestarting,
  } = useWhatsAppInstances();

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'open':
        return 'bg-green-500';
      case 'close':
        return 'bg-red-500';
      case 'connecting':
        return 'bg-yellow-500';
      default:
        return 'bg-gray-500';
    }
  };

  const getStatusText = (status: string) => {
    switch (status) {
      case 'open':
        return 'Conectado';
      case 'close':
        return 'Desconectado';
      case 'connecting':
        return 'Conectando';
      default:
        return 'Desconhecido';
    }
  };

  return (
    <div className="space-y-6">
      {/* Status and Actions */}
      <div className="flex items-center justify-between">
        <div className="flex items-center gap-3">
          <Badge
            variant="secondary"
            className={cn("text-white", getStatusColor(instance.connectionStatus))}
          >
            {getStatusText(instance.connectionStatus)}
          </Badge>
          <span className="text-lg font-semibold">{instance.name}</span>
        </div>
        
        <div className="flex gap-2">
          <Button
            onClick={() => connectInstance(instance.id)}
            disabled={isConnecting || instance.connectionStatus === 'open'}
            size="sm"
            variant="outline"
          >
            <Play className="h-4 w-4 mr-2" />
            {isConnecting ? 'Conectando...' : 'Conectar'}
          </Button>
          
          <Button
            onClick={() => disconnectInstance(instance.id)}
            disabled={isDisconnecting || instance.connectionStatus === 'close'}
            size="sm"
            variant="outline"
          >
            <Square className="h-4 w-4 mr-2" />
            {isDisconnecting ? 'Desconectando...' : 'Desconectar'}
          </Button>
          
          <Button
            onClick={() => restartInstance(instance.id)}
            disabled={isRestarting}
            size="sm"
            variant="outline"
          >
            <RotateCcw className="h-4 w-4 mr-2" />
            {isRestarting ? 'Reiniciando...' : 'Reiniciar'}
          </Button>
        </div>
      </div>

      <Separator />

      {/* Basic Information */}
      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        <Card>
          <CardHeader>
            <CardTitle className="text-sm font-medium flex items-center gap-2">
              <User className="h-4 w-4" />
              Informações Básicas
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-3">
            <div className="flex justify-between">
              <span className="text-sm text-muted-foreground">Número:</span>
              <span className="text-sm font-medium">{instance.number || 'N/A'}</span>
            </div>
            <div className="flex justify-between">
              <span className="text-sm text-muted-foreground">Nome do Perfil:</span>
              <span className="text-sm font-medium">{instance.profileName || 'N/A'}</span>
            </div>
            <div className="flex justify-between">
              <span className="text-sm text-muted-foreground">Cliente:</span>
              <span className="text-sm font-medium">{instance.clientName || 'N/A'}</span>
            </div>
            <div className="flex justify-between">
              <span className="text-sm text-muted-foreground">Integração:</span>
              <span className="text-sm font-medium">{instance.integration || 'N/A'}</span>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle className="text-sm font-medium flex items-center gap-2">
              <MessageSquare className="h-4 w-4" />
              Estatísticas
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-3">
            <div className="flex justify-between">
              <span className="text-sm text-muted-foreground">Mensagens:</span>
              <span className="text-sm font-medium">{instance._count.Message}</span>
            </div>
            <div className="flex justify-between">
              <span className="text-sm text-muted-foreground">Contatos:</span>
              <span className="text-sm font-medium">{instance._count.Contact}</span>
            </div>
            <div className="flex justify-between">
              <span className="text-sm text-muted-foreground">Chats:</span>
              <span className="text-sm font-medium">{instance._count.Chat}</span>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Settings */}
      {instance.Setting && (
        <Card>
          <CardHeader>
            <CardTitle className="text-sm font-medium flex items-center gap-2">
              <Settings className="h-4 w-4" />
              Configurações
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div className="space-y-3">
                <div className="flex items-center justify-between">
                  <span className="text-sm text-muted-foreground flex items-center gap-2">
                    <PhoneCall className="h-3 w-3" />
                    Rejeitar Chamadas
                  </span>
                  <Badge variant={instance.Setting.rejectCall ? "destructive" : "secondary"}>
                    {instance.Setting.rejectCall ? 'Sim' : 'Não'}
                  </Badge>
                </div>
                
                <div className="flex items-center justify-between">
                  <span className="text-sm text-muted-foreground flex items-center gap-2">
                    <Users className="h-3 w-3" />
                    Ignorar Grupos
                  </span>
                  <Badge variant={instance.Setting.groupsIgnore ? "destructive" : "secondary"}>
                    {instance.Setting.groupsIgnore ? 'Sim' : 'Não'}
                  </Badge>
                </div>
                
                <div className="flex items-center justify-between">
                  <span className="text-sm text-muted-foreground flex items-center gap-2">
                    <Clock className="h-3 w-3" />
                    Sempre Online
                  </span>
                  <Badge variant={instance.Setting.alwaysOnline ? "default" : "secondary"}>
                    {instance.Setting.alwaysOnline ? 'Sim' : 'Não'}
                  </Badge>
                </div>
              </div>
              
              <div className="space-y-3">
                <div className="flex items-center justify-between">
                  <span className="text-sm text-muted-foreground flex items-center gap-2">
                    <Eye className="h-3 w-3" />
                    Ler Mensagens
                  </span>
                  <Badge variant={instance.Setting.readMessages ? "default" : "secondary"}>
                    {instance.Setting.readMessages ? 'Sim' : 'Não'}
                  </Badge>
                </div>
                
                <div className="flex items-center justify-between">
                  <span className="text-sm text-muted-foreground flex items-center gap-2">
                    <EyeOff className="h-3 w-3" />
                    Status de Leitura
                  </span>
                  <Badge variant={instance.Setting.readStatus ? "default" : "secondary"}>
                    {instance.Setting.readStatus ? 'Sim' : 'Não'}
                  </Badge>
                </div>
                
                <div className="flex items-center justify-between">
                  <span className="text-sm text-muted-foreground flex items-center gap-2">
                    <Calendar className="h-3 w-3" />
                    Histórico Completo
                  </span>
                  <Badge variant={instance.Setting.syncFullHistory ? "default" : "secondary"}>
                    {instance.Setting.syncFullHistory ? 'Sim' : 'Não'}
                  </Badge>
                </div>
              </div>
            </div>
            
            {instance.Setting.msgCall && (
              <div className="mt-4 pt-4 border-t">
                <span className="text-sm text-muted-foreground">Mensagem de Chamada:</span>
                <p className="text-sm mt-1 p-2 bg-muted rounded">{instance.Setting.msgCall}</p>
              </div>
            )}
          </CardContent>
        </Card>
      )}

      {/* Timestamps */}
      <Card>
        <CardHeader>
          <CardTitle className="text-sm font-medium flex items-center gap-2">
            <Calendar className="h-4 w-4" />
            Datas
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-3">
          <div className="flex justify-between">
            <span className="text-sm text-muted-foreground">Criado em:</span>
            <span className="text-sm font-medium">
              {new Date(instance.createdAt).toLocaleString('pt-BR')}
            </span>
          </div>
          <div className="flex justify-between">
            <span className="text-sm text-muted-foreground">Atualizado em:</span>
            <span className="text-sm font-medium">
              {new Date(instance.updatedAt).toLocaleString('pt-BR')}
            </span>
          </div>
          {instance.disconnectionAt && (
            <div className="flex justify-between">
              <span className="text-sm text-muted-foreground">Desconectado em:</span>
              <span className="text-sm font-medium">
                {new Date(instance.disconnectionAt).toLocaleString('pt-BR')}
              </span>
            </div>
          )}
        </CardContent>
      </Card>
    </div>
  );
}
