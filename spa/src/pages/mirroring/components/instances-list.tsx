import { WhatsAppInstance } from '@/types';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Card, CardContent } from '@/components/ui/card';
import { Skeleton } from '@/components/ui/skeleton';
import { Smartphone, Phone, MessageSquare, Users, Clock } from 'lucide-react';
import { cn } from '@/lib/utils';

interface InstancesListProps {
  instances: WhatsAppInstance[];
  selectedInstance: string | null;
  onInstanceSelect: (instanceId: string) => void;
  isLoading: boolean;
}

export function InstancesList({
  instances,
  selectedInstance,
  onInstanceSelect,
  isLoading
}: InstancesListProps) {
  if (isLoading) {
    return (
      <div className="space-y-3">
        {[...Array(3)].map((_, i) => (
          <Card key={i}>
            <CardContent className="p-4">
              <div className="space-y-3">
                <Skeleton className="h-4 w-3/4" />
                <Skeleton className="h-3 w-1/2" />
                <div className="flex gap-2">
                  <Skeleton className="h-5 w-16" />
                  <Skeleton className="h-5 w-20" />
                </div>
              </div>
            </CardContent>
          </Card>
        ))}
      </div>
    );
  }

  if (instances.length === 0) {
    return (
      <div className="text-center py-8">
        <Smartphone className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
        <h3 className="text-lg font-medium">Nenhuma instância encontrada</h3>
        <p className="text-muted-foreground">
          Não há instâncias WhatsApp disponíveis no momento
        </p>
      </div>
    );
  }

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'open':
        return 'bg-green-500';
      case 'close':
        return 'bg-red-500';
      case 'connecting':
        return 'bg-yellow-500';
      default:
        return 'bg-gray-500';
    }
  };

  const getStatusText = (status: string) => {
    switch (status) {
      case 'open':
        return 'Conectado';
      case 'close':
        return 'Desconectado';
      case 'connecting':
        return 'Conectando';
      default:
        return 'Desconhecido';
    }
  };

  return (
    <div className="space-y-3">
      {instances.map((instance) => (
        <Card
          key={instance.id}
          className={cn(
            "cursor-pointer transition-all hover:shadow-md",
            selectedInstance === instance.id && "ring-2 ring-primary"
          )}
          onClick={() => onInstanceSelect(instance.id)}
        >
          <CardContent className="p-4">
            <div className="space-y-3">
              {/* Header */}
              <div className="flex items-center justify-between">
                <div className="flex items-center gap-2">
                  <Smartphone className="h-4 w-4 text-muted-foreground" />
                  <span className="font-medium">{instance.name}</span>
                </div>
                <Badge
                  variant="secondary"
                  className={cn("text-white", getStatusColor(instance.connectionStatus))}
                >
                  {getStatusText(instance.connectionStatus)}
                </Badge>
              </div>

              {/* Phone Number */}
              {instance.number && (
                <div className="flex items-center gap-2 text-sm text-muted-foreground">
                  <Phone className="h-3 w-3" />
                  <span>{instance.number}</span>
                </div>
              )}

              {/* Profile Name */}
              {instance.profileName && (
                <div className="text-sm text-muted-foreground">
                  {instance.profileName}
                </div>
              )}

              {/* Stats */}
              <div className="flex items-center gap-4 text-xs text-muted-foreground">
                <div className="flex items-center gap-1">
                  <MessageSquare className="h-3 w-3" />
                  <span>{instance._count.Message}</span>
                </div>
                <div className="flex items-center gap-1">
                  <Users className="h-3 w-3" />
                  <span>{instance._count.Contact}</span>
                </div>
                <div className="flex items-center gap-1">
                  <Clock className="h-3 w-3" />
                  <span>{instance._count.Chat}</span>
                </div>
              </div>

              {/* Last Update */}
              <div className="text-xs text-muted-foreground">
                Atualizado: {new Date(instance.updatedAt).toLocaleString('pt-BR')}
              </div>
            </div>
          </CardContent>
        </Card>
      ))}
    </div>
  );
}
