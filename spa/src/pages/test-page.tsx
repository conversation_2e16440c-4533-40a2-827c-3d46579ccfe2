import { Layout } from '@/components/layout';

export function TestPage() {
  return (
    <Layout.Root>
      <Layout.Breadcrumb
        links={[
          { name: "Home", href: "/" },
          { name: "Teste" },
        ]}
      />
      
      <div className="p-6">
        <h1 className="text-3xl font-bold">Página de Teste</h1>
        <p className="text-muted-foreground mt-2">
          Esta é uma página de teste para verificar se o sistema está funcionando.
        </p>
        
        <div className="mt-6 p-4 bg-green-100 border border-green-400 text-green-700 rounded">
          <h2 className="font-bold">✅ Sistema Funcionando!</h2>
          <p>Se você consegue ver esta página, o sistema está funcionando corretamente.</p>
        </div>
        
        <div className="mt-4">
          <h3 className="font-semibold mb-2">Informações do Sistema:</h3>
          <ul className="list-disc list-inside space-y-1 text-sm">
            <li>Frontend: React + Vite</li>
            <li>Backend: NestJS</li>
            <li>Autenticação: JWT</li>
            <li>Banco de Dados: PostgreSQL + Prisma</li>
          </ul>
        </div>
      </div>
    </Layout.Root>
  );
}
