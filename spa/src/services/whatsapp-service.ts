import { api } from './api';
import { WhatsAppInstance, WhatsAppGroup } from '@/types';

export class WhatsAppService {
  /**
   * Obtém a lista de instâncias do WhatsApp
   */
  static async getInstances(): Promise<WhatsAppInstance[]> {
    const response = await api.get<WhatsAppInstance[]>('/instances');
    return response.data;
  }

  /**
   * Obtém os detalhes de uma instância específica do WhatsApp
   */
  static async getInstanceDetails(instanceId: string): Promise<WhatsAppInstance> {
    const response = await api.get<WhatsAppInstance>(`/instances/${instanceId}`);
    return response.data;
  }

  /**
   * Conecta uma instância do WhatsApp
   */
  static async connectInstance(instanceId: string): Promise<{ message: string }> {
    const response = await api.post(`/instances/${instanceId}/connect`);
    return response.data;
  }

  /**
   * Desconecta uma instância do WhatsApp
   */
  static async disconnectInstance(instanceId: string): Promise<{ message: string }> {
    const response = await api.post(`/instances/${instanceId}/disconnect`);
    return response.data;
  }

  /**
   * Reinicia uma instância do WhatsApp
   */
  static async restartInstance(instanceId: string): Promise<{ message: string }> {
    const response = await api.post(`/instances/${instanceId}/restart`);
    return response.data;
  }

  /**
   * Busca todos os grupos de uma instância do WhatsApp
   */
  static async getInstanceGroups(instanceId: string): Promise<WhatsAppGroup[]> {
    const response = await api.get<WhatsAppGroup[]>(`/instances/${instanceId}/groups`);
    return response.data;
  }

  /**
   * Espelha um grupo do WhatsApp
   */
  static async mirrorGroup(instanceId: string, groupId: string): Promise<{ message: string }> {
    const response = await api.post(`/instances/${instanceId}/groups/${groupId}/mirror`);
    return response.data;
  }
}
