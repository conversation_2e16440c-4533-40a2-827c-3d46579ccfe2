import { useState, useCallback } from 'react';
import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { WhatsAppService } from '@/services/whatsapp-service';
import { WhatsAppInstance, WhatsAppGroup } from '@/types';
import { toast } from 'sonner';

export function useWhatsAppInstances() {
  const queryClient = useQueryClient();
  const [selectedInstance, setSelectedInstance] = useState<string | null>(null);

  // Query para listar instâncias
  const {
    data: instances = [],
    isLoading: isLoadingInstances,
    error: instancesError,
    refetch: refetchInstances
  } = useQuery({
    queryKey: ['whatsapp-instances'],
    queryFn: WhatsAppService.getInstances,
  });

  // Query para detalhes de uma instância específica
  const {
    data: instanceDetails,
    isLoading: isLoadingDetails,
    error: detailsError
  } = useQuery({
    queryKey: ['whatsapp-instance', selectedInstance],
    queryFn: () => selectedInstance ? WhatsAppService.getInstanceDetails(selectedInstance) : null,
    enabled: !!selectedInstance,
  });

  // Query para grupos de uma instância
  const {
    data: instanceGroups = [],
    isLoading: isLoadingGroups,
    error: groupsError,
    refetch: refetchGroups
  } = useQuery({
    queryKey: ['whatsapp-groups', selectedInstance],
    queryFn: () => selectedInstance ? WhatsAppService.getInstanceGroups(selectedInstance) : [],
    enabled: !!selectedInstance,
  });

  // Mutation para conectar instância
  const connectMutation = useMutation({
    mutationFn: WhatsAppService.connectInstance,
    onSuccess: (data) => {
      toast.success(data.message || 'Instância conectada com sucesso');
      queryClient.invalidateQueries({ queryKey: ['whatsapp-instances'] });
      if (selectedInstance) {
        queryClient.invalidateQueries({ queryKey: ['whatsapp-instance', selectedInstance] });
      }
    },
    onError: (error: any) => {
      toast.error(error?.response?.data?.message || 'Erro ao conectar instância');
    },
  });

  // Mutation para desconectar instância
  const disconnectMutation = useMutation({
    mutationFn: WhatsAppService.disconnectInstance,
    onSuccess: (data) => {
      toast.success(data.message || 'Instância desconectada com sucesso');
      queryClient.invalidateQueries({ queryKey: ['whatsapp-instances'] });
      if (selectedInstance) {
        queryClient.invalidateQueries({ queryKey: ['whatsapp-instance', selectedInstance] });
      }
    },
    onError: (error: any) => {
      toast.error(error?.response?.data?.message || 'Erro ao desconectar instância');
    },
  });

  // Mutation para reiniciar instância
  const restartMutation = useMutation({
    mutationFn: WhatsAppService.restartInstance,
    onSuccess: (data) => {
      toast.success(data.message || 'Instância reiniciada com sucesso');
      queryClient.invalidateQueries({ queryKey: ['whatsapp-instances'] });
      if (selectedInstance) {
        queryClient.invalidateQueries({ queryKey: ['whatsapp-instance', selectedInstance] });
      }
    },
    onError: (error: any) => {
      toast.error(error?.response?.data?.message || 'Erro ao reiniciar instância');
    },
  });

  // Mutation para espelhar grupo
  const mirrorGroupMutation = useMutation({
    mutationFn: ({ instanceId, groupId }: { instanceId: string; groupId: string }) =>
      WhatsAppService.mirrorGroup(instanceId, groupId),
    onSuccess: (data) => {
      toast.success(data.message || 'Grupo espelhado com sucesso');
      // Recarregar grupos automaticamente após espelhamento
      if (selectedInstance) {
        refetchGroups();
      }
    },
    onError: (error: any) => {
      toast.error(error?.response?.data?.message || 'Erro ao espelhar grupo');
    },
  });

  // Funções de ação
  const connectInstance = useCallback((instanceId: string) => {
    connectMutation.mutate(instanceId);
  }, [connectMutation]);

  const disconnectInstance = useCallback((instanceId: string) => {
    disconnectMutation.mutate(instanceId);
  }, [disconnectMutation]);

  const restartInstance = useCallback((instanceId: string) => {
    restartMutation.mutate(instanceId);
  }, [restartMutation]);

  const mirrorGroup = useCallback((instanceId: string, groupId: string) => {
    mirrorGroupMutation.mutate({ instanceId, groupId });
  }, [mirrorGroupMutation]);

  const selectInstance = useCallback((instanceId: string | null) => {
    setSelectedInstance(instanceId);
  }, []);

  return {
    // Data
    instances,
    instanceDetails,
    instanceGroups,
    selectedInstance,

    // Loading states
    isLoadingInstances,
    isLoadingDetails,
    isLoadingGroups,
    isConnecting: connectMutation.isPending,
    isDisconnecting: disconnectMutation.isPending,
    isRestarting: restartMutation.isPending,
    isMirroring: mirrorGroupMutation.isPending,

    // Error states
    instancesError,
    detailsError,
    groupsError,

    // Actions
    connectInstance,
    disconnectInstance,
    restartInstance,
    mirrorGroup,
    selectInstance,
    refetchInstances,
    refetchGroups,
  };
}
