import { Home } from "@/pages/home";
import { ListUsers, Profile, ChangePassword } from "@/pages/users";
import { FormChat, ListChats, UpdateChats } from "@/pages/chats";
import { Database, Instance, Roles, Params } from "@/pages/settings";
import { ListIncomingMessages } from "@/pages/chats";
import { GroupsInsurances } from "@/pages/groups-insurances";
import { GroupsUsers } from "@/pages/groups-users";
import { Permissions } from "@/pages/settings/permissions";
import { ListSentMessages } from "@/pages/chats/list-sent-messages";
import { ListPositiveMessages } from "@/pages/chats/list-positive-messages";
import { Dashboard } from "@/pages/dashboard";
import { Mirroring } from "@/pages/mirroring";
import { TestPage } from "@/pages/test-page";

export interface RoutesProps {
  path: string;
  element: JSX.Element;
  can: string[];
}

export const routes: RoutesProps[] = [
  { path: "/", element: <Home />, can: [] },
  { path: "/home", element: <Home />, can: [] },
  { path: "/dashboard", element: <Dashboard />, can: [] },
  { path: "/me", element: <Profile />, can: [] },
  { path: "/users", element: <ListUsers />, can: ["r-user"] },
  { path: "/profile", element: <Profile />, can: [] },
  { path: "/change-password", element: <ChangePassword />, can: [] },
  { path: "/chat", element: <FormChat />, can: ["c-chat"] },
  { path: "/chat/:id", element: <FormChat />, can: ["u-chat"] },
  { path: "/chats", element: <ListChats />, can: ["r-chat", "user"] },
  { path: "/chats-update", element: <UpdateChats />, can: ["u-chat"] },
  { path: "/groups-insurances", element: <GroupsInsurances />, can: ["r-group"] },
  { path: "/groups-users", element: <GroupsUsers />, can: ["r-group"] },
  { path: "/settings/instance", element: <Instance />, can: ["admin", "manager"] },
  { path: "/settings/params", element: <Params />, can: ["admin", "manager"] },
  { path: "/settings/database", element: <Database />, can: ["admin"] },
  { path: "/settings/roles", element: <Roles />, can: ["admin"] },
  { path: "/settings/permissions", element: <Permissions />, can: ["admin"] },
  { path: "/chats/list-incoming-messages", element: <ListIncomingMessages />, can: [] },
  { path: "/chats/list-sent-messages", element: <ListSentMessages />, can: [] },
  { path: "/chats/list-positive-messages", element: <ListPositiveMessages />, can: [] },
  { path: "/mirroring", element: <Mirroring />, can: [] },
  { path: "/test", element: <TestPage />, can: [] },
];
